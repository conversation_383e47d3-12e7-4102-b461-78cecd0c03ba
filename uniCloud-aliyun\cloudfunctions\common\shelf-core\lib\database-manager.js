'use strict'

/**
 * 数据库操作管理器
 * 统一处理所有数据库相关操作，减少重复代码
 */
class DatabaseManager {
  constructor() {
    this.db = uniCloud.database()
    this.dbCmd = this.db.command
  }

  /**
   * 获取平台配置
   * @param {string} userId 用户ID
   * @param {string} platformType 平台类型（可选）
   * @param {boolean} activeOnly 是否只获取启用的配置
   * @returns {Promise<Array>} 平台配置列表
   */
  async getPlatformConfigs(userId, platformType = null, activeOnly = true) {
    try {
      const whereCondition = {
        user_id: userId
      }
      
      if (platformType) {
        whereCondition.platform_type = platformType
      }
      
      if (activeOnly) {
        whereCondition.status = 1
      }

      const result = await this.db.collection('platform-configs')
        .where(whereCondition)
        .orderBy('create_time', 'desc')
        .get()

      return result.data
    } catch (error) {
      console.error('获取平台配置失败:', error)
      throw error
    }
  }

  /**
   * 更新平台配置
   * @param {string} userId 用户ID
   * @param {string} platformType 平台类型
   * @param {Object} updateData 更新数据
   * @returns {Promise<void>}
   */
  async updatePlatformConfig(userId, platformType, updateData) {
    try {
      await this.db.collection('platform-configs')
        .where({
          user_id: userId,
          platform_type: platformType
        })
        .update({
          ...updateData,
          update_time: new Date()
        })
    } catch (error) {
      console.error('更新平台配置失败:', error)
      throw error
    }
  }

  /**
   * 根据配置ID精确更新平台配置
   * @param {string} configId 配置ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<void>}
   */
  async updatePlatformConfigById(configId, updateData) {
    try {
      // {{ AURA-X: Add - 基于配置ID的精确更新方法，避免误更新多个配置. Approval: 寸止(ID:1735373000). }}
      await this.db.collection('platform-configs')
        .doc(configId)
        .update({
          ...updateData,
          update_time: new Date()
        })
    } catch (error) {
      console.error('根据ID更新平台配置失败:', error)
      throw error
    }
  }

  /**
   * 保存或更新平台配置
   * @param {string} userId 用户ID
   * @param {Object} configData 配置数据
   * @returns {Promise<void>}
   */
  async savePlatformConfig(userId, configData) {
    try {
      const {
        platformType,
        platformName,
        username,
        password,
        token,
        cookie,
        headers,
        autoLogin,
        remark
      } = configData

      // 检查是否已存在相同平台配置
      const existingConfig = await this.db.collection('platform-configs')
        .where({
          user_id: userId,
          platform_type: platformType
        })
        .get()

      const configRecord = {
        user_id: userId,
        platform_name: platformName,
        platform_type: platformType,
        username: username || '',
        password: password || '',
        token: token || '',
        cookie: cookie || '',
        headers: headers || {},
        auto_login: autoLogin || false,
        login_status: 0,
        status: 1,
        remark: remark || '',
        update_time: new Date()
      }

      if (existingConfig.data.length > 0) {
        // 更新现有配置
        await this.db.collection('platform-configs')
          .doc(existingConfig.data[0]._id)
          .update(configRecord)
      } else {
        // 创建新配置
        configRecord.create_time = new Date()
        await this.db.collection('platform-configs').add(configRecord)
      }
    } catch (error) {
      console.error('保存平台配置失败:', error)
      throw error
    }
  }

  /**
   * 获取货架列表
   * @param {string} userId 用户ID
   * @param {Object} queryOptions 查询选项
   * @returns {Promise<Object>} 货架列表和分页信息
   */
  async getShelfList(userId, queryOptions = {}) {
    try {
      const {
        platformType,
        gameAccount,
        status,
        pageIndex = 1,
        pageSize = 20,
        activeOnly = null
      } = queryOptions

      const whereCondition = {
        user_id: userId
      }

      if (platformType) {
        whereCondition.platform_type = platformType
      }

      if (gameAccount) {
        whereCondition.game_account = this.dbCmd.regex({
          regexp: gameAccount,
          options: 'i'
        })
      }

      if (status !== undefined) {
        whereCondition.unified_state = status
      }

      if (activeOnly !== null) {
        whereCondition.is_active = activeOnly
      }

      const shelves = await this.db.collection('account-shelves')
        .where(whereCondition)
        .orderBy('last_sync_time', 'desc')
        .skip((pageIndex - 1) * pageSize)
        .limit(pageSize)
        .get()

      const total = await this.db.collection('account-shelves')
        .where(whereCondition)
        .count()

      return {
        list: shelves.data,
        total: total.total,
        pageIndex,
        pageSize
      }
    } catch (error) {
      console.error('获取货架列表失败:', error)
      throw error
    }
  }

  /**
   * 批量更新或插入货架数据
   * @param {string} userId 用户ID
   * @param {string} platformType 平台类型
   * @param {Array} shelfList 货架列表
   * @returns {Promise<number>} 更新的货架数量
   */
  async batchUpsertShelves(userId, platformType, shelfList) {
    try {
      const updatePromises = shelfList.map(async (shelf) => {
        const existingShelf = await this.db.collection('account-shelves')
          .where({
            user_id: userId,
            platform_type: platformType,
            platform_shelf_id: shelf.id
          })
          .get()

        const shelfRecord = {
          user_id: userId,
          platform_type: platformType,
          platform_shelf_id: shelf.id,
          game_account: shelf.game_account,
          game_name: shelf.game_name,
          game_role_name: shelf.game_role_name,
          shelf_title: shelf.shelf_title,
          rent_price: shelf.rent_price,
          min_rent_time: shelf.min_rent_time,
          unified_state: shelf.unified_state,
          platform_status: JSON.stringify(shelf.platform_status),
          last_sync_time: new Date(),
          update_time: new Date()
        }

        if (existingShelf.data.length > 0) {
          // 更新现有货架
          await this.db.collection('account-shelves')
            .doc(existingShelf.data[0]._id)
            .update(shelfRecord)
        } else {
          // 创建新货架
          shelfRecord.create_time = new Date()
          shelfRecord.is_active = true
          await this.db.collection('account-shelves').add(shelfRecord)
        }
      })

      await Promise.all(updatePromises)
      return shelfList.length
    } catch (error) {
      console.error('批量更新货架失败:', error)
      throw error
    }
  }

  /**
   * 更新货架状态
   * @param {string} shelfId 货架ID
   * @param {Object} statusData 状态数据
   * @returns {Promise<void>}
   */
  async updateShelfStatus(shelfId, statusData) {
    try {
      await this.db.collection('account-shelves')
        .doc(shelfId)
        .update({
          unified_state: statusData.unified_state,
          platform_status: JSON.stringify(statusData.platform_status),
          last_sync_time: new Date(),
          update_time: new Date()
        })
    } catch (error) {
      console.error('更新货架状态失败:', error)
      throw error
    }
  }

  /**
   * 更新货架监控状态
   * @param {string} shelfId 货架ID
   * @param {boolean} isActive 是否启用监控
   * @returns {Promise<void>}
   */
  async updateShelfMonitorStatus(shelfId, isActive) {
    try {
      await this.db.collection('account-shelves')
        .doc(shelfId)
        .update({
          is_active: isActive,
          update_time: new Date()
        })
    } catch (error) {
      console.error('更新货架监控状态失败:', error)
      throw error
    }
  }

  /**
   * 获取操作日志
   * @param {string} userId 用户ID
   * @param {Object} queryOptions 查询选项
   * @returns {Promise<Object>} 日志列表和分页信息
   */
  async getOperationLogs(userId, queryOptions = {}) {
    try {
      const {
        platformType,
        action,
        status,
        pageIndex = 1,
        pageSize = 20
      } = queryOptions

      // 构建查询条件，忽略空字符串参数
      const whereCondition = { user_id: userId }

      if (platformType && platformType.trim()) {
        whereCondition.platform_type = platformType
      }

      if (action && action.trim()) {
        whereCondition.action = action
      }

      if (status !== undefined && status !== '') {
        whereCondition.status = status
      }

      // 并行执行查询和计数
      const [logs, total] = await Promise.all([
        this.db.collection('operation-logs')
          .where(whereCondition)
          .orderBy('create_time', 'desc')
          .skip((pageIndex - 1) * pageSize)
          .limit(pageSize)
          .get(),
        this.db.collection('operation-logs')
          .where(whereCondition)
          .count()
      ])

      return {
        list: logs.data,
        total: total.total,
        pageIndex,
        pageSize
      }
    } catch (error) {
      console.error('获取操作日志失败:', error)
      throw error
    }
  }

  /**
   * 清理过期日志
   * @param {number} daysToKeep 保留天数
   * @returns {Promise<void>}
   */
  async cleanupExpiredLogs(daysToKeep = 1) {
    try {
      const expireDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000)
      await this.db.collection('operation-logs')
        .where({
          create_time: this.dbCmd.lt(expireDate)
        })
        .remove()
      console.log(`清理过期日志完成，保留${daysToKeep}天`)
    } catch (error) {
      console.error('清理过期日志失败:', error)
      throw error
    }
  }
}

module.exports = DatabaseManager
