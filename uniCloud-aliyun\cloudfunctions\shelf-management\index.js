'use strict'
// {{ AURA-X: Modify - 移除直接数据库操作，统一使用DatabaseManager. Approval: 寸止(ID:**********). }}
const db = uniCloud.database() // 仅用于用户token验证
const dbCmd = db.command
// {{ AURA-X: Modify - 使用公共模块替代重复代码. Approval: 寸止(ID:1735372800). }}
const { PlatformAdapterFactory, StateManager, Logger, DatabaseManager } = require('shelf-core')
/**
 * 验证token并获取用户ID
 */
async function verifyToken(token) {
  if (!token) {
    return null
  }
  
  try {
    // 查找token对应的用户
    const userResult = await db.collection('uni-id-users')
      .where({
        'token.token': token,
        'token.expire': dbCmd.gt(new Date()),
        status: 0
      })
      .get()
    
    if (userResult.data.length > 0) {
      return userResult.data[0]._id
    }
    
    return null
  } catch (error) {
    console.error('验证token失败:', error)
    return null
  }
}
/**
 * 货架管理云函数
 * 提供客户端调用的各种管理接口
 */
exports.main = async (event, context) => {
  const { action, data, _token } = event
  
  // 验证用户登录状态
  const uid = await verifyToken(_token)
  if (!uid) {
    return {
      code: -2,
      message: '用户未登录'
    }
  }
  
  try {
    switch (action) {
      case 'getPlatformList':
        return await getPlatformList()
      case 'savePlatformConfig':
        return await savePlatformConfig(uid, data)
      case 'getPlatformConfigs':
        return await getPlatformConfigs(uid)
      case 'deletePlatformConfig':
        return await deletePlatformConfig(uid, data.configId)
      case 'testPlatformLogin':
        return await testPlatformLogin(uid, data)
      case 'syncShelves':
        return await syncShelves(uid, data.platformType)
      case 'getShelfList':
        return await getShelfList(uid, data)
      case 'toggleShelfStatus':
        return await toggleShelfStatus(uid, data)
      case 'toggleMonitorStatus':
        return await toggleMonitorStatus(uid, data)
      case 'getOperationLogs':
        return await getOperationLogs(uid, data)

      default:
        return {
          code: -1,
          message: '不支持的操作'
        }
    }
  } catch (error) {
    console.error('云函数执行失败:', error)
    return {
      code: -1,
      message: error.message || '操作失败'
    }
  }
}
/**
 * 获取支持的平台列表
 */
async function getPlatformList() {
  try {
    const platforms = PlatformAdapterFactory.getSupportedPlatforms()
    return {
      code: 0,
      data: platforms,
      message: '获取成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 保存平台配置
 */
async function savePlatformConfig(userId, configData) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()
    await dbManager.savePlatformConfig(userId, configData)

    return {
      code: 0,
      message: '保存成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 获取用户的平台配置列表
 */
async function getPlatformConfigs(userId) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()
    const configs = await dbManager.getPlatformConfigs(userId, null, true)

    // 隐藏敏感信息
    const safeConfigs = configs.map(config => {
      delete config.password
      delete config.token
      delete config.cookie
      delete config.headers
      return config
    })

    return {
      code: 0,
      data: safeConfigs,
      message: '获取成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 删除平台配置
 */
async function deletePlatformConfig(userId, configId) {
  try {
    await db.collection('platform-configs')
      .where({
        _id: configId,
        user_id: userId
      })
      .update({
        status: 0,
        update_time: new Date()
      })
    return {
      code: 0,
      message: '删除成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 测试平台登录
 */
async function testPlatformLogin(userId, configData) {
  try {
    console.log('开始测试平台登录')
    console.log('用户ID:', userId)
    console.log('前端传递的配置数据:', configData)

    // {{ AURA-X: Modify - 使用配置ID精确获取配置，避免误操作多个同类型配置. Approval: 寸止(ID:1735373000). }}
    const dbManager = new DatabaseManager()
    let finalConfig = { ...configData }

    // 如果提供了配置ID，从数据库获取完整配置信息
    if (configData.configId) {
      // {{ AURA-X: Fix - 修复uniCloud数据库查询语法错误. Approval: 寸止(ID:1735373100). }}
      const result = await db.collection('platform-configs')
        .where({
          _id: configData.configId,
          user_id: userId // 确保安全性，只能操作自己的配置
        })
        .get()

      if (result.data.length > 0) {
        const existingConfig = result.data[0]
        console.log('找到数据库中的配置')

        // 使用数据库中的真实数据
        finalConfig.username = existingConfig.username
        finalConfig.password = existingConfig.password
        finalConfig.token = existingConfig.token
        finalConfig.cookie = existingConfig.cookie
        finalConfig.headers = existingConfig.headers || {}
        finalConfig._id = existingConfig._id // 保存配置ID用于后续更新
      }
    } else {
      // 兼容旧版本：如果没有配置ID，使用原有逻辑
      const dbConfigs = await dbManager.getPlatformConfigs(userId, configData.platformType, false)
      if (dbConfigs.length > 0) {
        const existingConfig = dbConfigs[0]
        console.log('找到数据库中的配置（兼容模式）')

        finalConfig.username = existingConfig.username
        finalConfig.password = existingConfig.password
        finalConfig.token = existingConfig.token
        finalConfig.cookie = existingConfig.cookie
        finalConfig.headers = existingConfig.headers || {}
        finalConfig._id = existingConfig._id
      }
    }

    console.log('最终使用的配置数据:', {
      configId: finalConfig._id,
      platformType: finalConfig.platformType,
      username: finalConfig.username,
      hasPassword: !!finalConfig.password,
      hasToken: !!finalConfig.token,
      hasCookie: !!finalConfig.cookie
    })

    const adapter = PlatformAdapterFactory.create(finalConfig.platformType, {
      ...finalConfig,
      user_id: userId
    })

    console.log('适配器创建成功，开始登录')
    const loginResult = await adapter.login()
    console.log('登录结果:', loginResult)

    // {{ AURA-X: Delete - 移除云函数层的重复数据库更新，适配器内部已处理. Approval: 寸止(ID:1735373000). }}
    // 适配器内部已经处理了数据库更新，这里不再重复更新

    return {
      code: loginResult.success ? 0 : -1,
      message: loginResult.message
    }
  } catch (error) {
    console.error('测试平台登录异常:', error)
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 更新货架数据
 */
async function syncShelves(userId, platformType) {
  try {
    const logger = new Logger()
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    // 获取平台配置
    const dbManager = new DatabaseManager()
    const platformConfigs = await dbManager.getPlatformConfigs(userId, platformType, true)

    if (platformConfigs.length === 0) {
      throw new Error('未找到平台配置')
    }
    const config = platformConfigs[0]
    const adapter = PlatformAdapterFactory.create(platformType, config)
    // {{ AURA-X: Modify - 优化登录状态检查，登录状态检测已移至统一的API响应处理中. Approval: 寸止(ID:**********). }}
    // 检查基本登录状态（token是否存在）
    const isLoggedIn = await adapter.checkLoginStatus()
    if (!isLoggedIn) {
      // 尝试自动登录
      if (config.auto_login) {
        const loginResult = await adapter.login()
        if (!loginResult.success) {
          throw new Error('登录失败: ' + loginResult.message)
        }
      } else {
        throw new Error('平台未登录，请手动更新Cookie')
      }
    }
    // 获取货架列表
    const shelfList = await adapter.getShelfList()
    // {{ AURA-X: Modify - 使用DatabaseManager简化批量货架操作. Approval: 寸止(ID:**********). }}
    // 批量更新或插入货架数据
    await dbManager.batchUpsertShelves(userId, platformType, shelfList)
    // 记录更新日志
    await logger.log({
      user_id: userId,
      platform_type: platformType,
      action: 'sync',
      status: 1,
      message: `更新成功，共更新 ${shelfList.length} 个货架`,
      trigger_type: 'manual'
    })
    return {
      code: 0,
      data: {
        syncCount: shelfList.length
      },
      message: `更新成功，共更新 ${shelfList.length} 个货架`
    }
  } catch (error) {
    const logger = new Logger()
    await logger.log({
      user_id: userId,
      platform_type: platformType,
      action: 'sync',
      status: 0,
      message: error.message,
      trigger_type: 'manual'
    })
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 获取货架列表
 */
async function getShelfList(userId, queryData = {}) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()
    const result = await dbManager.getShelfList(userId, queryData)

    return {
      code: 0,
      data: result,
      message: '获取成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 手动切换货架状态
 */
async function toggleShelfStatus(userId, shelfData) {
  try {
    const { shelfId, targetStatus } = shelfData
    const logger = new Logger()
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()

    // 获取货架信息
    const shelfResult = await dbManager.getShelfList(userId, { pageSize: 1000 })
    const shelfInfo = shelfResult.list.find(s => s._id === shelfId)

    if (!shelfInfo) {
      throw new Error('货架不存在')
    }

    // 获取平台配置
    const platformConfigs = await dbManager.getPlatformConfigs(userId, shelfInfo.platform_type, true)
    if (platformConfigs.length === 0) {
      throw new Error('平台配置不存在')
    }
    const config = platformConfigs[0]
    const adapter = PlatformAdapterFactory.create(shelfInfo.platform_type, config)
    // 验证目标状态
    if (!StateManager.isValidState(targetStatus)) {
      throw new Error('无效的目标状态')
    }

    // 执行上下架操作
    let result
    let action
    if (targetStatus === StateManager.STATES.AVAILABLE) {
      // 上架
      result = await adapter.onShelf(shelfInfo.platform_shelf_id)
      action = 'on_shelf'
    } else if (targetStatus === StateManager.STATES.OFFLINE) {
      // 下架
      result = await adapter.offShelf(shelfInfo.platform_shelf_id)
      action = 'off_shelf'
    } else {
      throw new Error('不支持的目标状态')
    }
    // 记录操作日志
    await logger.log({
      user_id: userId,
      platform_type: shelfInfo.platform_type,
      platform_shelf_id: shelfInfo.platform_shelf_id,
      game_account: shelfInfo.game_account,
      action: action,
      status: result.success ? 1 : 0,
      message: result.message,
      trigger_type: 'manual'
    })
    if (result.success) {
      // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
      // 更新本地状态
      await dbManager.updateShelfStatus(shelfId, {
        unified_state: targetStatus,
        platform_status: {}
      })
    }
    return {
      code: result.success ? 0 : -1,
      message: result.message
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 切换货架监控状态
 */
async function toggleMonitorStatus(userId, shelfData) {
  try {
    const { shelfId, isActive } = shelfData
    const logger = new Logger()
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()

    // 获取货架信息
    const shelfResult = await dbManager.getShelfList(userId, { pageSize: 1000 })
    const shelfInfo = shelfResult.list.find(s => s._id === shelfId)

    if (!shelfInfo) {
      throw new Error('货架不存在')
    }

    // 更新监控状态
    await dbManager.updateShelfMonitorStatus(shelfId, isActive)
    
    // 记录操作日志
    await logger.log({
      user_id: userId,
      platform_type: shelfInfo.platform_type,
      platform_shelf_id: shelfInfo.platform_shelf_id,
      game_account: shelfInfo.game_account,
      action: isActive ? 'enable_monitor' : 'disable_monitor',
      status: 1,
      message: isActive ? '开启监控' : '关闭监控',
      trigger_type: 'manual'
    })
    
    return {
      code: 0,
      message: isActive ? '已开启监控' : '已关闭监控'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 获取操作日志
 */
async function getOperationLogs(userId, queryData = {}) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()
    const result = await dbManager.getOperationLogs(userId, queryData)

    return {
      code: 0,
      data: result,
      message: '获取成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
